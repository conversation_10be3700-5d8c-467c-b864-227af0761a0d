services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: agent
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5433:5432"  # Change the port to 5433 to avoid conflicts with the local PostgreSQL
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 5

  xbit-agent:
    build: .
    ports:
      - "8080:8080"
    env_file:
      - ./env/docker.env
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./config.yaml:/root/config.yaml
      - ./log:/root/log

volumes:
  postgres_data:
