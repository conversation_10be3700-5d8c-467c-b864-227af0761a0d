package app

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/playground"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_error"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
)

type GraphqlServer struct {
	Router     *gin.Engine
	HttpServer *http.Server
	ctx        context.Context
	Cancel     context.CancelFunc
}

func (server *GraphqlServer) Initialize() {
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initializer.Gorm()
	initializer.DBList()

	if global.GVA_CONFIG.System.UseMultipoint || global.GVA_CONFIG.System.UseRedis {
		// init redis server
		// initializer.Redis()
		// initializer.RedisList()
	}

	// Register database tables
	initializer.RegisterTables()

	// Initialize NATS clients - only using nats-meme for now
	// initializer.InitNats() // Temporarily disabled - only using nats-meme for now
	initializer.InitNatsMeme()
	// initializer.InitNatsDex() // Temporarily disabled - not yet implemented for affiliate events

	// Create context for NATS subscribers
	server.ctx, server.Cancel = context.WithCancel(context.Background())

	// Start affiliate subscriber
	if err := initializer.StartAffiliateSubscriber(server.ctx); err != nil {
		global.GVA_LOG.Error("Failed to start affiliate subscriber", zap.Error(err))
	}
}

func (server *GraphqlServer) Run() {
	if server.Router == nil {
		server.Router = server.setupRouter()
	}

	address := fmt.Sprintf(":%d", global.GVA_CONFIG.System.Addr)
	server.HttpServer = &http.Server{
		Addr:           address,
		Handler:        server.Router,
		ReadTimeout:    20 * time.Second,
		WriteTimeout:   20 * time.Second,
		MaxHeaderBytes: 1 << 20,
	}

	global.GVA_LOG.Info("server run success on ", zap.String("address", address))

	fmt.Printf(`
	Welcome to XBIT Agent
	Current Version: v1.0.0
	GraphQL Playground: http://127.0.0.1%s%s/playground
	GraphQL Endpoint: http://127.0.0.1%s%s
`, address, global.GVA_CONFIG.System.GraphqlPrefix, address, global.GVA_CONFIG.System.GraphqlPrefix)

	log.Fatal(server.HttpServer.ListenAndServe())
}

func (server *GraphqlServer) setupRouter() *gin.Engine {
	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS configuration
	// corsConfig := cors.Config{
	// 	AllowOrigins:     []string{"*"},
	// 	AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
	// 	AllowHeaders:     []string{"Origin", "Content-Length", "Content-Type", "Authorization", "X-Requested-With"},
	// 	ExposeHeaders:    []string{"Content-Length", "Access-Control-Allow-Origin", "Access-Control-Allow-Headers", "Content-Type"},
	// 	AllowCredentials: true,
	// 	MaxAge:           12 * time.Hour,
	// }
	// router.Use(cors.New(corsConfig))
	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"*"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))
	// router.Use(middlewares.ErrorHandler())
	// router.Use(middlewares.RequestLogger())
	router.Use(graphql.GinGqlContext()) // this will attach gin.Context into graphql context

	// Health check routes (following xbit-goback convention)
	pingHandler := func(c *gin.Context) {
		c.JSON(200, gin.H{
			"pong": time.Now().UnixMilli(),
		})
	}

	// GraphQL API group with prefix
	api := router.Group(global.GVA_CONFIG.System.GraphqlPrefix)
	api.GET("/ping", pingHandler)
	api.GET("/healthz", pingHandler)

	// Initialize GraphQL server with the same API group
	initGraphqlServer(router)

	return router
}

func initGraphqlServer(router *gin.Engine) {
	api := router.Group(global.GVA_CONFIG.System.GraphqlPrefix)

	// GraphQL playground handler
	playGroundHandler := playground.Handler("XBIT Agent: GraphQL playground", global.GVA_CONFIG.System.GraphqlPrefix)

	// Create JWT middleware
	// authMiddleware := graphql.NewAuthMiddleware(global.GVA_CONFIG.JWT)

	// GraphQL resolver with JWT config
	// resolver := graphql.NewResolver(global.GVA_CONFIG.JWT)

	// GraphQL server
	graphqlServer := handler.NewDefaultServer(graphql.NewExecutableSchema(graphql.Config{
		// Resolvers:  resolver,
		Directives: graphql.DirectiveRoot{},
	}))

	graphqlServer.SetErrorPresenter(gql_error.CustomErrorPresenter)
	// GraphQL playground endpoint (no auth required)
	api.GET("/playground", func(c *gin.Context) {
		playGroundHandler.ServeHTTP(c.Writer, c.Request)
	})

	api.POST("", graphql.GqlJwtAuth(), func(c *gin.Context) {
		graphqlServer.ServeHTTP(c.Writer, c.Request)
	})
	// GraphQL endpoint (with auth)
	// api.POST("", func(c *gin.Context) {
	// 	// Apply JWT middleware for authentication
	// 	authMiddleware.HTTPMiddleware(graphqlServer).ServeHTTP(c.Writer, c.Request)
	// })
}
