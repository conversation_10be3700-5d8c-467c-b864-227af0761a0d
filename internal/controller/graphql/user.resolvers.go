package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.76

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
)

// CreateUserWithReferral is the resolver for the createUserWithReferral field.
func (r *mutationResolver) CreateUserWithReferral(ctx context.Context, input gql_model.CreateUserWithReferralInput) (*gql_model.CreateUserResponse, error) {
	referrer, err := r.UserService.GetUserByInvitationCode(ctx, input.InvitationCode)
	if err != nil || referrer == nil {
		return nil, fmt.Errorf("Invalid invitation code")
	}

	err = r.UserService.CreateUserWithReferral(ctx, referrer.ID, input.UserID)
	if err != nil {
		return nil, fmt.Errorf("Failed to create user association: %v", err)
	}

	return &gql_model.CreateUserResponse{
		Success: true,
		Message: "User created and bound referral relationship successfully",
	}, nil
}

// CreateUser is the resolver for the createUser field.
func (r *mutationResolver) CreateUser(ctx context.Context, input gql_model.CreateUserInput) (*gql_model.CreateUserResponse, error) {
	// _, err := GetUserIDFromContext(ctx)
	// if err != nil {
	// 	return &gql_model.CreateUserResponse{
	// 		User:    nil,
	// 		Success: false,
	// 		Message: "Unauthorized: missing or invalid token",
	// 	}, nil
	// }

	invitationCode := ""
	if input.InvitationCode != nil {
		invitationCode = *input.InvitationCode
	}

	referrerCode := ""
	if input.ReferrerCode != nil {
		referrerCode = *input.ReferrerCode
	}

	user, err := r.UserService.CreateUser(ctx, input.Email, invitationCode, referrerCode)
	if err != nil {
		return &gql_model.CreateUserResponse{
			User:    nil,
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &gql_model.CreateUserResponse{
		User:    ModelUserToGQL(user),
		Success: true,
		Message: "User created successfully",
	}, nil
}

// CreateUserWallet is the resolver for the createUserWallet field.
func (r *mutationResolver) CreateUserWallet(ctx context.Context, input gql_model.CreateUserWalletInput) (*gql_model.CreateUserWalletResponse, error) {
	// authUserID, err := GetUserIDFromContext(ctx)
	// if err != nil {
	// 	return &gql_model.CreateUserWalletResponse{
	// 		Wallet:  nil,
	// 		Success: false,
	// 		Message: "Unauthorized: missing or invalid token",
	// 	}, nil
	// }

	requestUserID, err := uuid.Parse(input.UserID)
	if err != nil {
		return &gql_model.CreateUserWalletResponse{
			Wallet:  nil,
			Success: false,
			Message: "Invalid user ID",
		}, nil
	}

	// if authUserID != requestUserID {
	// 	return &gql_model.CreateUserWalletResponse{
	// 		Wallet:  nil,
	// 		Success: false,
	// 		Message: "Unauthorized: can only create wallet for your own account",
	// 	}, nil
	// }

	var walletID, walletAccountID *uuid.UUID
	if input.WalletID != nil {
		id, err := uuid.Parse(*input.WalletID)
		if err != nil {
			return &gql_model.CreateUserWalletResponse{
				Wallet:  nil,
				Success: false,
				Message: "Invalid wallet ID",
			}, nil
		}
		walletID = &id
	}

	if input.WalletAccountID != nil {
		id, err := uuid.Parse(*input.WalletAccountID)
		if err != nil {
			return &gql_model.CreateUserWalletResponse{
				Wallet:  nil,
				Success: false,
				Message: "Invalid wallet account ID",
			}, nil
		}
		walletAccountID = &id
	}

	name := ""
	if input.Name != nil {
		name = *input.Name
	}

	walletType := GQLWalletTypeToString(input.WalletType)

	wallet, err := r.UserService.CreateUserWallet(ctx, requestUserID, input.Chain, name, input.WalletAddress, walletID, walletAccountID, walletType)
	if err != nil {
		return &gql_model.CreateUserWalletResponse{
			Wallet:  nil,
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &gql_model.CreateUserWalletResponse{
		Wallet:  ModelUserWalletToGQL(wallet),
		Success: true,
		Message: "Wallet created successfully",
	}, nil
}

// UpdateFirstLoginStatus is the resolver for the updateFirstLoginStatus field.
func (r *mutationResolver) UpdateFirstLoginStatus(ctx context.Context, userID string) (*gql_model.User, error) {
	// authUserID, err := GetUserIDFromContext(ctx)
	// if err != nil {
	// 	return nil, fmt.Errorf("unauthorized: missing or invalid token")
	// }

	id, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// if authUserID != id {
	// 	return nil, fmt.Errorf("unauthorized: can only update your own status")
	// }

	user, err := r.UserService.UpdateFirstLoginStatus(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to update first login status: %w", err)
	}

	return ModelUserToGQL(user), nil
}

// UpdateWalletExportStatus is the resolver for the updateWalletExportStatus field.
func (r *mutationResolver) UpdateWalletExportStatus(ctx context.Context, userID string) (*gql_model.User, error) {
	// authUserID, err := GetUserIDFromContext(ctx)
	// if err != nil {
	// 	return nil, fmt.Errorf("unauthorized: missing or invalid token")
	// }

	id, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// if authUserID != id {
	// 	return nil, fmt.Errorf("unauthorized: can only update your own status")
	// }

	user, err := r.UserService.UpdateWalletExportStatus(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to update wallet export status: %w", err)
	}

	return ModelUserToGQL(user), nil
}

// CreateUserInvitationCode is the resolver for the createUserInvitationCode field.
func (r *mutationResolver) CreateUserInvitationCode(ctx context.Context, input gql_model.CreateUserInvitationCodeInput) (*gql_model.CreateUserResponse, error) {
	// authUserID, err := GetUserIDFromContext(ctx)
	// if err != nil {
	// 	return nil, fmt.Errorf("unauthorized: missing or invalid token")
	// }

	// 确保用户只能更新自己的邀请码
	// if authUserID != userID {
	// 	return nil, fmt.Errorf("unauthorized: can only update your own invitation code")
	// }

	// userID, err := uuid.Parse(input.UserID)
	// if err != nil {
	// 	return nil, fmt.Errorf("invalid user ID: %w", err)
	// }

	requestUserID, err := uuid.Parse(input.UserID)
	if err != nil {
		return &gql_model.CreateUserResponse{
			User:    nil,
			Success: false,
			Message: "Invalid user ID",
		}, nil
	}

	var walletID, walletAccountID *uuid.UUID
	if input.WalletID != nil {
		id, err := uuid.Parse(*input.WalletID)
		if err != nil {
			return &gql_model.CreateUserResponse{
				User:    nil,
				Success: false,
				Message: "Invalid wallet ID",
			}, nil
		}
		walletID = &id
	}

	if input.WalletAccountID != nil {
		id, err := uuid.Parse(*input.WalletAccountID)
		if err != nil {
			return &gql_model.CreateUserResponse{
				User:    nil,
				Success: false,
				Message: "Invalid wallet account ID",
			}, nil
		}
		walletAccountID = &id
	}

	name := ""
	if input.Name != nil {
		name = *input.Name
	}
	chain := ""
	if input.Chain != nil {
		chain = *input.Chain
	}
	walletAddress := ""
	if input.WalletAddress != nil {
		walletAddress = *input.WalletAddress
	}
	email := ""
	if input.Email != nil {
		email = *input.Email
	}
	isFirstLogin := false
	if input.IsFirstLogin != nil {
		isFirstLogin = *input.IsFirstLogin
	}
	isExportedWallet := false
	if input.IsExportedWallet != nil {
		isExportedWallet = *input.IsExportedWallet
	}

	walletType := GQLWalletTypeToString(input.WalletType)

	user, err := r.UserService.UpdateUserInvitationCode(ctx, requestUserID,
		chain, name, walletAddress, walletID, walletAccountID,
		walletType, input.InvitationCode, email, isFirstLogin, isExportedWallet)
	if err != nil {
		return nil, fmt.Errorf("failed to update user invitation code: %w", err)
	}

	return &gql_model.CreateUserResponse{
		User:    ModelUserToGQL(user),
		Success: true,
		Message: "Wallet created successfully",
	}, nil
}

// User is the resolver for the user field.
func (r *queryResolver) User(ctx context.Context, id string) (*gql_model.User, error) {
	// authUserID, err := GetUserIDFromContext(ctx)
	// if err != nil {
	// 	return nil, fmt.Errorf("unauthorized: missing or invalid token")
	// }

	userID, err := uuid.Parse(id)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// if authUserID != userID {
	// 	return nil, fmt.Errorf("unauthorized: can only view your own information")
	// }

	user, err := r.UserService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return ModelUserToGQL(user), nil
}

// UserByEmail is the resolver for the userByEmail field.
func (r *queryResolver) UserByEmail(ctx context.Context, email string) (*gql_model.User, error) {
	// authUserID, err := GetUserIDFromContext(ctx)
	// if err != nil {
	// 	return nil, fmt.Errorf("unauthorized: missing or invalid token")
	// }

	user, err := r.UserService.GetUserByEmail(ctx, email)
	if err != nil {
		return nil, fmt.Errorf("failed to get user by email: %w", err)
	}

	// if authUserID != user.ID {
	// 	return nil, fmt.Errorf("unauthorized: can only view your own information")
	// }

	return ModelUserToGQL(user), nil
}

// UserWallets is the resolver for the userWallets field.
func (r *queryResolver) UserWallets(ctx context.Context, userID string) ([]*gql_model.UserWallet, error) {
	// authUserID, err := GetUserIDFromContext(ctx)
	// if err != nil {
	// 	return nil, fmt.Errorf("unauthorized: missing or invalid token")
	// }

	id, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// if authUserID != id {
	// 	return nil, fmt.Errorf("unauthorized: can only view your own wallets")
	// }

	wallets, err := r.UserService.GetUserWallets(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get user wallets: %w", err)
	}

	var gqlWallets []*gql_model.UserWallet
	for _, wallet := range wallets {
		gqlWallets = append(gqlWallets, ModelUserWalletToGQL(&wallet))
	}

	return gqlWallets, nil
}

// ReferralInfo is the resolver for the referralInfo field.
func (r *queryResolver) ReferralInfo(ctx context.Context, userID string) (*gql_model.Referral, error) {
	// authUserID, err := GetUserIDFromContext(ctx)
	// if err != nil {
	// 	return nil, fmt.Errorf("unauthorized: missing or invalid token")
	// }

	id, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// if authUserID != id {
	// 	return nil, fmt.Errorf("unauthorized: can only view your own referral info")
	// }

	referral, err := r.UserService.GetReferralInfo(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get referral info: %w", err)
	}

	return ModelReferralToGQL(referral), nil
}

// ReferralSnapshot is the resolver for the referralSnapshot field.
func (r *queryResolver) ReferralSnapshot(ctx context.Context, userID string) (*gql_model.ReferralSnapshot, error) {
	// authUserID, err := GetUserIDFromContext(ctx)
	// if err != nil {
	// 	return nil, fmt.Errorf("unauthorized: missing or invalid token")
	// }

	id, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// if authUserID != id {
	// 	return nil, fmt.Errorf("unauthorized: can only view your own referral snapshot")
	// }

	snapshot, err := r.UserService.GetReferralSnapshot(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get referral snapshot: %w", err)
	}

	return ModelReferralSnapshotToGQL(snapshot), nil
}

// Downlines is the resolver for the downlines field.
func (r *queryResolver) Downlines(ctx context.Context, userID string) ([]*gql_model.Referral, error) {
	// authUserID, err := GetUserIDFromContext(ctx)
	// if err != nil {
	// 	return nil, fmt.Errorf("unauthorized: missing or invalid token")
	// }

	id, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// if authUserID != id {
	// 	return nil, fmt.Errorf("unauthorized: can only view your own downlines")
	// }

	downlines, err := r.UserService.GetDownlines(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get downlines: %w", err)
	}

	var gqlDownlines []*gql_model.Referral
	for _, downline := range downlines {
		gqlDownlines = append(gqlDownlines, ModelReferralToGQL(&downline))
	}

	return gqlDownlines, nil
}

// GetUserInvitationCode is the resolver for the getUserInvitationCode field.
func (r *queryResolver) GetUserInvitationCode(ctx context.Context, userID string) (*string, error) {
	panic(fmt.Errorf("not implemented: GetUserInvitationCode - getUserInvitationCode"))
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
