package graphql

import (
	"context"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
)

type Claims struct {
	Sub string `json:"sub"`
	jwt.RegisteredClaims
}

func GqlJwtAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		consumerName := ctx.GetHeader("X-Consumer-Username")
		if consumerName != "xbit" {
			ctx.Next()
		}

		authHeader := ctx.GetHeader("Authorization")
		if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
			ctx.Next()
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")

		claims := &Claims{}
		_, _, err := new(jwt.Parser).ParseUnverified(tokenString, claims)

		if err != nil {
			ctx.Next()
		}

		wrappedCtx := context.WithValue(ctx.Request.Context(), "userId", claims.Sub)
		ctx.Request = ctx.Request.WithContext(wrappedCtx)
		ctx.Next()
	}
}
