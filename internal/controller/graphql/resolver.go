package graphql

import (
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
)

// This file will not be regenerated automatically.
//
// It serves as dependency injection for your app, add any dependencies you require here.

type Resolver struct {
	UserRepo    repo.UserRepositoryInterface
	UserService service.UserServiceInterface
	JWTConfig   config.JWT
}

func NewResolver(jwtConfig config.JWT) *Resolver {
	return &Resolver{
		UserRepo:    repo.NewUserRepository(),
		UserService: service.NewUserService(),
		JWTConfig:   jwtConfig,
	}
}
