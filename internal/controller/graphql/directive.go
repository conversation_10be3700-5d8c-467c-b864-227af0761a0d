package graphql

import (
	"context"

	"github.com/99designs/gqlgen/graphql"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

func AuthDirective(ctx context.Context, obj interface{}, next graphql.Resolver) (interface{}, error) {
	// For now, we'll implement a simple auth check
	// In a real implementation, you would check JWT tokens, session, etc.

	// Get user from context (this would be set by middleware)
	user := ctx.Value("userId")
	if user == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	return next(ctx)
}
