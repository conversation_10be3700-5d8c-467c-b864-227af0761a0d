package graphql

import (
	"context"

	"github.com/gin-gonic/gin"
)

// Define a key for storing gin.Context in context.Context
type contextKey string

const GinContextKey contextKey = "ginContext"

// Middleware to add gin.Context to context.Context of graphql
// So many feature that we need to access gin.Context from resolver, service, etc.
func GinGqlContext() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := context.WithValue(c.Request.Context(), GinContextKey, c)
		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}
