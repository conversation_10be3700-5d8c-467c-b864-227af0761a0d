package initializer

import (
	"os"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"

	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

func Gorm() *gorm.DB {
	switch global.GVA_CONFIG.System.DbType {
	case "pgsql":
		global.GVA_ACTIVE_DBNAME = &global.GVA_CONFIG.Pgsql.Dbname
		return GormPgSql()
	default:
		global.GVA_ACTIVE_DBNAME = &global.GVA_CONFIG.Pgsql.Dbname
		return GormPgSql()
	}
}

func RegisterTables() {
	db := global.GVA_DB
	if db == nil {
		global.GVA_LOG.Error("database connection is nil, cannot register tables")
		os.Exit(1)
	}

	err := db.AutoMigrate(
		&model.User{},
		&model.UserWallet{},
		&model.Referral{},
		&model.ReferralSnapshot{},
		&model.AffiliateTransaction{},
		&model.SolPriceSnapshot{},
	)
	if err != nil {
		global.GVA_LOG.Error("register table failed", zap.Error(err))
		os.Exit(1)
	}

	global.GVA_LOG.Info("register table success")
}

func gormConfig(prefix string, singular bool) *gorm.Config {
	config := &gorm.Config{
		DisableForeignKeyConstraintWhenMigrating: true,
		Logger:                                   logger.Default.LogMode(logger.Silent),
	}
	_default := schema.NamingStrategy{
		TablePrefix:   prefix,
		SingularTable: singular,
	}
	config.NamingStrategy = _default
	return config
}
