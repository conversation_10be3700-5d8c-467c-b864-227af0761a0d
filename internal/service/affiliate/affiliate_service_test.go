package affiliate

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	natsModel "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
)

// setupTest initializes the test environment
func setupTest() {
	// Initialize a no-op logger for testing
	global.GVA_LOG = zap.NewNop()
}

// MockAffiliateRepository is a mock implementation of AffiliateRepositoryInterface
type MockAffiliateRepository struct {
	mock.Mock
}

func (m *MockAffiliateRepository) CreateAffiliateTransaction(ctx context.Context, tx *model.AffiliateTransaction) error {
	args := m.Called(ctx, tx)
	return args.Error(0)
}

func (m *MockAffiliateRepository) GetAffiliateTransactionByOrderID(ctx context.Context, orderID uuid.UUID) (*model.AffiliateTransaction, error) {
	args := m.Called(ctx, orderID)
	return args.Get(0).(*model.AffiliateTransaction), args.Error(1)
}

func (m *MockAffiliateRepository) GetAffiliateTransactionByTxHash(ctx context.Context, txHash string) (*model.AffiliateTransaction, error) {
	args := m.Called(ctx, txHash)
	return args.Get(0).(*model.AffiliateTransaction), args.Error(1)
}

func (m *MockAffiliateRepository) UpdateAffiliateTransaction(ctx context.Context, tx *model.AffiliateTransaction) error {
	args := m.Called(ctx, tx)
	return args.Error(0)
}

func (m *MockAffiliateRepository) GetAffiliateTransactionsByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.AffiliateTransaction, error) {
	args := m.Called(ctx, userID, limit, offset)
	return args.Get(0).([]model.AffiliateTransaction), args.Error(1)
}

func (m *MockAffiliateRepository) GetAffiliateTransactionsByReferrerID(ctx context.Context, referrerID uuid.UUID, limit, offset int) ([]model.AffiliateTransaction, error) {
	args := m.Called(ctx, referrerID, limit, offset)
	return args.Get(0).([]model.AffiliateTransaction), args.Error(1)
}

func (m *MockAffiliateRepository) GetUnpaidCommissions(ctx context.Context, referrerID uuid.UUID) ([]model.AffiliateTransaction, error) {
	args := m.Called(ctx, referrerID)
	return args.Get(0).([]model.AffiliateTransaction), args.Error(1)
}

func (m *MockAffiliateRepository) MarkCommissionAsPaid(ctx context.Context, transactionIDs []uint) error {
	args := m.Called(ctx, transactionIDs)
	return args.Error(0)
}

func (m *MockAffiliateRepository) UpsertSolPriceSnapshot(ctx context.Context, snapshot *model.SolPriceSnapshot) error {
	args := m.Called(ctx, snapshot)
	return args.Error(0)
}

func (m *MockAffiliateRepository) GetLatestSolPrice(ctx context.Context) (*model.SolPriceSnapshot, error) {
	args := m.Called(ctx)
	return args.Get(0).(*model.SolPriceSnapshot), args.Error(1)
}

func (m *MockAffiliateRepository) GetSolPriceHistory(ctx context.Context, from, to time.Time) ([]model.SolPriceSnapshot, error) {
	args := m.Called(ctx, from, to)
	return args.Get(0).([]model.SolPriceSnapshot), args.Error(1)
}

func (m *MockAffiliateRepository) GetUserTotalVolume(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockAffiliateRepository) GetReferrerTotalCommission(ctx context.Context, referrerID uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, referrerID)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockAffiliateRepository) GetReferrerUnpaidCommission(ctx context.Context, referrerID uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, referrerID)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

// MockUserRepository is a mock implementation of UserRepositoryInterface
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) Create(ctx context.Context, user *model.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) CreateWallet(ctx context.Context, wallet *model.UserWallet) error {
	args := m.Called(ctx, wallet)
	return args.Error(0)
}

func (m *MockUserRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) GetByEmail(ctx context.Context, email string) (*model.User, error) {
	args := m.Called(ctx, email)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) GetByInvitationCode(ctx context.Context, code string) (*model.User, error) {
	args := m.Called(ctx, code)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) Update(ctx context.Context, user *model.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserRepository) GetUserWallets(ctx context.Context, userID uuid.UUID) ([]model.UserWallet, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]model.UserWallet), args.Error(1)
}

func (m *MockUserRepository) CreateUserWallet(ctx context.Context, wallet *model.UserWallet) error {
	args := m.Called(ctx, wallet)
	return args.Error(0)
}

func (m *MockUserRepository) GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.Referral), args.Error(1)
}

func (m *MockUserRepository) GetDirectReferral(ctx context.Context, userID, referrerID uuid.UUID) (*model.Referral, error) {
	args := m.Called(ctx, userID, referrerID)
	return args.Get(0).(*model.Referral), args.Error(1)
}

func (m *MockUserRepository) GetAllReferrals(ctx context.Context, userID uuid.UUID) ([]model.Referral, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]model.Referral), args.Error(1)
}

func (m *MockUserRepository) CreateReferral(ctx context.Context, referral *model.Referral) error {
	args := m.Called(ctx, referral)
	return args.Error(0)
}

func (m *MockUserRepository) GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.ReferralSnapshot), args.Error(1)
}

func (m *MockUserRepository) UpdateReferralSnapshot(ctx context.Context, snapshot *model.ReferralSnapshot) error {
	args := m.Called(ctx, snapshot)
	return args.Error(0)
}

func (m *MockUserRepository) GetDownlines(ctx context.Context, userID uuid.UUID) ([]model.Referral, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]model.Referral), args.Error(1)
}

func (m *MockUserRepository) GetReferrerByUserID(ctx context.Context, referrerID uuid.UUID) (model.Referral, error) {
	args := m.Called(ctx, referrerID)
	return args.Get(0).(model.Referral), args.Error(1)
}

func (m *MockUserRepository) GetReferrerByReferrerID(ctx context.Context, referrerID uuid.UUID) (model.Referral, error) {
	args := m.Called(ctx, referrerID)
	return args.Get(0).(model.Referral), args.Error(1)
}

func TestProcessSolPriceUpdate(t *testing.T) {
	setupTest()

	// Create service with mock repositories
	service := &AffiliateService{
		affiliateRepo: &MockAffiliateRepository{},
		userRepo:      &MockUserRepository{},
	}

	// Test data
	priceEvent := &natsModel.SolPriceEvent{
		UsdPrice:  decimal.NewFromFloat(100.50),
		Timestamp: time.Now().Unix(),
	}

	// Test ProcessSolPriceUpdate
	err := service.ProcessSolPriceUpdate(context.Background(), priceEvent)

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, priceEvent.UsdPrice, service.latestSolPrice)
	assert.Equal(t, priceEvent.GetTime(), service.latestSolPriceTime)
}

func TestSaveSolPriceSnapshot(t *testing.T) {
	setupTest()

	mockRepo := &MockAffiliateRepository{}
	service := &AffiliateService{
		affiliateRepo:      mockRepo,
		userRepo:           &MockUserRepository{},
		latestSolPrice:     decimal.NewFromFloat(100.50),
		latestSolPriceTime: time.Now(),
	}

	// Setup mock expectation
	mockRepo.On("UpsertSolPriceSnapshot", mock.Anything, mock.AnythingOfType("*model.SolPriceSnapshot")).Return(nil)

	// Test saveSolPriceSnapshot
	err := service.saveSolPriceSnapshot(context.Background())

	// Assertions
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
}
