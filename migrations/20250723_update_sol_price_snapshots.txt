-- -- Update sol_price_snapshots table structure
-- -- Remove unnecessary fields and add updated_at field
-- -- Add unique constraint on timestamp for millisecond precision deduplication

-- -- Drop existing indexes first
-- DROP INDEX IF EXISTS "idx_sol_price_snapshots_symbol";

-- -- Remove unnecessary columns
-- ALTER TABLE "public"."sol_price_snapshots" DROP COLUMN IF EXISTS "symbol";
-- ALTER TABLE "public"."sol_price_snapshots" DROP COLUMN IF EXISTS "chain_id";
-- ALTER TABLE "public"."sol_price_snapshots" DROP COLUMN IF EXISTS "address";
-- ALTER TABLE "public"."sol_price_snapshots" DROP COLUMN IF EXISTS "created_at";

-- -- Add updated_at column
-- ALTER TABLE "public"."sol_price_snapshots" ADD COLUMN "updated_at" timestamptz NULL;

-- -- Create unique index on timestamp to ensure only one record per millisecond
-- DROP INDEX IF EXISTS "idx_sol_price_snapshots_timestamp";
-- CREATE UNIQUE INDEX "idx_sol_price_snapshots_timestamp_unique" ON "public"."sol_price_snapshots" ("timestamp");

-- -- Update existing records to set updated_at = current timestamp
-- UPDATE "public"."sol_price_snapshots" SET "updated_at" = CURRENT_TIMESTAMP WHERE "updated_at" IS NULL;
