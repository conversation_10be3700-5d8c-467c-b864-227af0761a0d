#!/bin/sh
set -e

_terminate() {
  echo "Signal (SIGINT/SIGTERM) received. Waiting for gracefully shutdown..."
  kill $(jobs -p)
  wait
  exit 0
}

trap _terminate SIGINT SIGTERM

# Run migration
export DATABASE_URL="postgresql://$POSTGRES_USER:$POSTGRES_PASS@$POSTGRES_HOST:$POSTGRES_PORT/${POSTGRES_DB:-agent}?sslmode=disable"
echo "DATABASE_URL: postgresql://$POSTGRES_USER:***@$POSTGRES_HOST:$POSTGRES_PORT/${POSTGRES_DB:-agent}?sslmode=disable"

# Wait for database to be ready (optional but recommended)
echo "Waiting for database to be ready..."
for i in $(seq 1 30); do
    if atlas schema inspect --url "$DATABASE_URL" >/dev/null 2>&1; then
        echo "✅ Database connection successful."
        break
    else
        echo "⏳ Waiting for database... (attempt $i/30)"
        sleep 2
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Database connection failed after 30 attempts."
        exit 1
    fi
done

# Check if migrations directory exists and has files
if [ -d "migrations" ] && [ "$(ls -A migrations)" ]; then
    echo "Migration files found. Running Atlas migrations..."
    echo "Available migration files:"
    ls -la migrations/*.sql 2>/dev/null || echo "No .sql files found"

    # Run Atlas migrations
    if command -v atlas >/dev/null 2>&1; then
        echo "Atlas CLI found. Running migrations..."
        echo "Database URL: $DATABASE_URL"

        # Check migration status first
        echo "Checking migration status..."
        atlas migrate status --dir "file://migrations" --url "$DATABASE_URL" 2>/tmp/status_check.log || true

        # Try to run migration, handle both clean and dirty database scenarios
        echo "Attempting to run Atlas migrations..."

        # First, try normal migration
        echo "Executing: atlas migrate apply --dir file://migrations --url [DATABASE_URL]"
        atlas migrate apply --dir "file://migrations" --url "$DATABASE_URL" 2>/tmp/migration_error.log
        migration_exit_code=$?

        # If failed due to dirty database, try with baseline
        if [ $migration_exit_code -ne 0 ]; then
            if grep -q "not clean\|baseline.*required" /tmp/migration_error.log; then
                echo "Database is not clean, retrying with baseline..."
                echo "Executing: atlas migrate apply --dir file://migrations --url [DATABASE_URL] --baseline"
                atlas migrate apply --dir "file://migrations" --url "$DATABASE_URL" --baseline 2>/tmp/migration_error2.log
                migration_exit_code=$?

                # If baseline also fails, try with allow-dirty
                if [ $migration_exit_code -ne 0 ]; then
                    echo "Baseline approach failed, trying with --allow-dirty..."
                    echo "Executing: atlas migrate apply --dir file://migrations --url [DATABASE_URL] --allow-dirty"
                    atlas migrate apply --dir "file://migrations" --url "$DATABASE_URL" --allow-dirty
                    migration_exit_code=$?
                fi
            else
                echo "Migration failed with different error:"
                cat /tmp/migration_error.log
            fi
        fi

        # Clean up temporary files
        rm -f /tmp/migration_error.log /tmp/migration_error2.log /tmp/status_check.log

        if [ $migration_exit_code -eq 0 ]; then
            echo "✅ Atlas migrations completed successfully."
        else
            echo "❌ Atlas migrations failed with exit code: $migration_exit_code"
            echo "All migration approaches failed:"
            echo "1. Normal migration"
            echo "2. Baseline migration"
            echo "3. Allow-dirty migration"
            echo "Please check the database connection and migration files manually."
            exit 1
        fi
    else
        echo "❌ Atlas CLI not found. Please install Atlas or use manual migration."
        echo "You can install Atlas with: curl -sSf https://atlasgo.sh | sh"
        exit 1
    fi
else
    echo "No migration files found or migrations directory doesn't exist."
    echo "Skipping migrations."
fi

echo "Database migration process completed. Starting application..."

# Wait for any process to exit
# wait -n

# Exit with status of process that exited first
exec $@
