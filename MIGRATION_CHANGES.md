# Database Migration Changes

## Overview
This document describes the changes made to enable Atlas migrations for the `sol_price_snapshots` table schema updates.

## Problem
The `sol_price_snapshots` table was not being updated during deployment because:

1. **Atlas migrations were disabled** in `entrypoint.sh`
2. **GORM AutoMigrate limitations** - cannot drop columns or modify indexes
3. **Invalid migration file** - had `.txt` extension and commented SQL

## Solution Applied

### 1. Enabled Atlas Migrations in `entrypoint.sh`

**Before:**
```bash
echo "Skipping Atlas migrations - using GORM AutoMigrate for database schema management."
```

**After:**
```bash
# Run Atlas migrations with multiple fallback strategies
# 1. Normal migration
atlas migrate apply --dir "file://migrations" --url "$DATABASE_URL"
# 2. If database not clean, use baseline
atlas migrate apply --dir "file://migrations" --url "$DATABASE_URL" --baseline
# 3. If baseline fails, use allow-dirty
atlas migrate apply --dir "file://migrations" --url "$DATABASE_URL" --allow-dirty
```

### 2. Added Database Connection Check
- Added retry logic (30 attempts, 2-second intervals)
- Validates database connectivity before running migrations
- Provides clear error messages

### 3. Enhanced Error Handling for "Database Not Clean"
- **Multiple fallback strategies** for existing databases
- **Automatic detection** of "not clean" errors
- **Baseline migration** for databases with existing schema
- **Allow-dirty migration** as final fallback
- **Status check** before applying migrations

### 4. Enhanced Logging
- Shows available migration files
- Provides detailed success/failure feedback
- Masks sensitive database credentials in logs
- Captures and analyzes error messages

## Migration Details

### File: `migrations/20250723050339.sql`
This migration will:

1. **Drop old index:**
   ```sql
   DROP INDEX "public"."idx_sol_price_snapshots_timestamp";
   ```

2. **Modify table structure:**
   ```sql
   ALTER TABLE "public"."sol_price_snapshots"
   DROP COLUMN "symbol",
   DROP COLUMN "chain_id",
   DROP COLUMN "address",
   DROP COLUMN "created_at",
   ADD COLUMN "updated_at" timestamptz NULL;
   ```

3. **Create unique index:**
   ```sql
   CREATE UNIQUE INDEX "idx_sol_price_snapshots_timestamp"
   ON "public"."sol_price_snapshots" ("timestamp");
   ```

## Expected Results After Deployment

### Table Structure Changes
- ❌ Removed: `symbol`, `chain_id`, `address`, `created_at`
- ✅ Added: `updated_at` (timestamptz)
- ✅ Unique constraint on `timestamp` (millisecond precision)

### Application Behavior Changes
- SOL price snapshots only saved when affiliate transactions occur
- Upsert functionality prevents duplicate timestamps
- Thread-safe price storage in memory

## Deployment Process

1. **Build new Docker image** with updated `entrypoint.sh`
2. **Deploy to environment** - Atlas migrations will run automatically
3. **Verify migration success** - Check logs for "✅ Atlas migrations completed successfully"
4. **Validate table structure** - Confirm columns are dropped/added correctly

## Rollback Plan

If issues occur, you can:

1. **Revert entrypoint.sh** to disable Atlas migrations
2. **Manual database restore** from backup if needed
3. **Run manual SQL** to fix any partial migration issues

## Files Modified

- `entrypoint.sh` - Enabled Atlas migrations with enhanced error handling
- `migrations/20250723050339.sql` - Contains the schema changes
- `migrations/atlas.sum` - Updated checksums

## Verification Commands

After deployment, verify the changes:

```sql
-- Check table structure
\d sol_price_snapshots

-- Verify unique index exists
\di *sol_price_snapshots*

-- Check for any existing data
SELECT COUNT(*) FROM sol_price_snapshots;
```

## Notes

- Migration is **idempotent** - safe to run multiple times
- **Backup recommended** before deployment
- **Monitor logs** during deployment for any issues
- **Test in staging** environment first if possible
