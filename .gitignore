# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build artifacts
bin/
dist/

# Logs
log/
*.log

# Environment files
.env.local
.env.production
.env.staging

# Keep example files and env directory
!.env.example
!env/

# IDE files
.vscode/
.idea/
*.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/

# Database files
*.db
*.sqlite
*.sqlite3

# Coverage reports
coverage.out
coverage.html

# Air live reload
.air.toml

# Docker volumes
postgres_data/
redis_data/

# Atlas migration files are now tracked in git (following xbit-goback approach)
# migrations/*.sql
# !migrations/README.md

# Generated files
internal/controller/graphql/generated.go
internal/controller/graphql/gql_model/models_gen.go

# Backup files
*.bak
*.backup

# Local configuration overrides
config.local.yaml
config.override.yaml

# Glide
.glide/
.env
